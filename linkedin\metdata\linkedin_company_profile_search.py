#!/usr/bin/env python
"""
LinkedIn Company Profile Search Automation
This script automates the process of searching for LinkedIn company profiles using Google search,
validates URLs, and stores results in Excel format.
"""

import os
import time
import sys
import json
import re
import random
import pandas as pd
import logging
from datetime import datetime
from selenium import webdriver
from selenium.webdriver.chrome.options import Options
from selenium.webdriver.common.by import By
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC
from selenium.webdriver.common.keys import Keys
from selenium.common.exceptions import (
    TimeoutException, NoSuchElementException, ElementClickInterceptedException,
    StaleElementReferenceException, WebDriverException
)
from urllib.parse import urlparse, parse_qs
import urllib.parse

class LinkedInCompanyProfileSearch:
    """Automates LinkedIn company profile search using Google search"""

    def __init__(self, headless=False, credentials=None):
        """Initialize the company profile search automation"""
        self.driver = None
        self.headless = headless
        self.credentials = credentials or {}
        self.search_results = []
        self.processed_companies = 0
        self.successful_searches = 0
        self.failed_searches = 0
        
        # Setup logging
        self.setup_logging()
        
        # LinkedIn company profile URL patterns (regex) - Updated to include country domains
        self.linkedin_company_patterns = [
            r'https?://(?:www\.)?(?:[a-z]{2}\.)?linkedin\.com/company/[^/\s?#]+/?(?:\?.*)?(?:#.*)?$',
            r'https?://(?:www\.)?(?:[a-z]{2}\.)?linkedin\.com/company/[^/\s?#]+/about/?(?:\?.*)?(?:#.*)?$',
            r'https?://(?:www\.)?(?:[a-z]{2}\.)?linkedin\.com/company/[^/\s?#]+/jobs/?(?:\?.*)?(?:#.*)?$',
            r'https?://(?:www\.)?(?:[a-z]{2}\.)?linkedin\.com/company/[^/\s?#]+/people/?(?:\?.*)?(?:#.*)?$'
        ]
        
        # Invalid URL patterns to exclude (but allow company profiles)
        self.invalid_patterns = [
            r'linkedin\.com/in/',  # Personal profiles
            r'linkedin\.com/posts/',  # Posts
            r'linkedin\.com/feed/',  # Feed
            r'linkedin\.com/search/',  # Search results
            r'linkedin\.com/jobs/view/',  # Individual job listings
            r'linkedin\.com/learning/',  # Learning content
            r'linkedin\.com/events/',  # Events
            r'linkedin\.com/groups/',  # Groups
            r'linkedin\.com/showcase/',  # Showcase pages
            r'linkedin\.com/pulse/',  # Articles
            r'linkedin\.com/help/',  # Help pages
        ]
        
        # Google search selectors (updated for current Google structure)
        self.google_selectors = {
            'search_results': [
                'a[jsname="UWckNb"]',  # Current Google structure
                'div[data-ved] a[href*="linkedin.com"]',
                '.g a[href*="linkedin.com"]',
                'a[href*="linkedin.com"]',
                'div[data-ved] h3 a',
                '.g h3 a',
                '[data-ved] h3 a',
                '.yuRUbf a',
                'h3.LC20lb a',
                '.tF2Cxc h3 a',
                '.r a h3'
            ],
            'search_box': [
                'input[name="q"]',
                'textarea[name="q"]',
                '#APjFqb',
                '.gLFyf',
                'input[title="Search"]',
                'input[type="text"]',
                'textarea[jsaction*="paste"]',
                '.a4bIc input',
                '#searchboxinput',
                'input[aria-label*="Search"]'
            ],
            'search_button': [
                'input[type="submit"]',
                'button[type="submit"]',
                '.gNO89b',
                '.Tg7LZd',
                'input[value="Google Search"]'
            ]
        }

    def setup_logging(self):
        """Setup comprehensive logging"""
        log_filename = f"linkedin_company_search_{datetime.now().strftime('%Y%m%d_%H%M%S')}.log"
        
        logging.basicConfig(
            level=logging.INFO,
            format='%(asctime)s - %(levelname)s - %(message)s',
            handlers=[
                logging.FileHandler(log_filename),
                logging.StreamHandler(sys.stdout)
            ]
        )
        self.logger = logging.getLogger(__name__)
        self.logger.info("LinkedIn Company Profile Search automation initialized")

    def setup_driver(self):
        """Setup Chrome WebDriver with optimized settings"""
        try:
            chrome_options = Options()
            
            if self.headless:
                chrome_options.add_argument("--headless")
            
            # Performance and stealth options
            chrome_options.add_argument("--no-sandbox")
            chrome_options.add_argument("--disable-dev-shm-usage")
            chrome_options.add_argument("--disable-gpu")
            chrome_options.add_argument("--window-size=1920,1080")
            chrome_options.add_argument("--disable-blink-features=AutomationControlled")
            chrome_options.add_experimental_option("excludeSwitches", ["enable-automation"])
            chrome_options.add_experimental_option('useAutomationExtension', False)
            
            # User agent to avoid detection
            chrome_options.add_argument("--user-agent=Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36")
            
            # Additional stealth options
            chrome_options.add_argument("--disable-extensions")
            chrome_options.add_argument("--disable-plugins-discovery")
            chrome_options.add_argument("--disable-web-security")
            chrome_options.add_argument("--allow-running-insecure-content")
            
            self.driver = webdriver.Chrome(options=chrome_options)
            self.driver.execute_script("Object.defineProperty(navigator, 'webdriver', {get: () => undefined})")
            
            self.logger.info("Chrome WebDriver initialized successfully")
            return True
            
        except Exception as e:
            self.logger.error(f"Failed to setup WebDriver: {e}")
            return False

    def intelligent_wait(self, min_seconds=2, max_seconds=5):
        """Intelligent wait with random delays to avoid detection"""
        wait_time = random.uniform(min_seconds, max_seconds)
        time.sleep(wait_time)
        return wait_time

    def find_element_with_multiple_selectors(self, selectors, timeout=10):
        """Try multiple selectors to find an element"""
        wait = WebDriverWait(self.driver, timeout)
        
        for selector in selectors:
            try:
                element = wait.until(EC.presence_of_element_located((By.CSS_SELECTOR, selector)))
                return element
            except TimeoutException:
                continue
        
        return None

    def find_elements_with_multiple_selectors(self, selectors, timeout=10):
        """Try multiple selectors to find elements"""
        for selector in selectors:
            try:
                elements = WebDriverWait(self.driver, timeout).until(
                    EC.presence_of_all_elements_located((By.CSS_SELECTOR, selector))
                )
                if elements:
                    return elements
            except TimeoutException:
                continue
        
        return []

    def is_valid_linkedin_company_url(self, url):
        """Check if URL is a valid LinkedIn company profile URL"""
        if not url:
            return False
            
        # Check for invalid patterns first
        for pattern in self.invalid_patterns:
            if re.search(pattern, url, re.IGNORECASE):
                return False
        
        # Check for valid LinkedIn company patterns
        for pattern in self.linkedin_company_patterns:
            if re.match(pattern, url, re.IGNORECASE):
                return True
        
        return False

    def extract_company_name_from_url(self, url):
        """Extract company name from LinkedIn company URL"""
        try:
            # Pattern to extract company name from LinkedIn URL
            match = re.search(r'linkedin\.com/company/([^/?]+)', url, re.IGNORECASE)
            if match:
                return match.group(1)
        except Exception as e:
            self.logger.error(f"Error extracting company name from URL {url}: {e}")
        return None

    def load_excel_data(self, excel_path):
        """Load company names from Excel file"""
        try:
            df = pd.read_excel(excel_path)
            if 'Company_Name' not in df.columns:
                raise ValueError("Excel file must contain 'Company_Name' column")
            
            # Get unique company names and remove any NaN values
            company_names = df['Company_Name'].dropna().unique().tolist()
            self.logger.info(f"Loaded {len(company_names)} unique company names from Excel file")
            return company_names
            
        except Exception as e:
            self.logger.error(f"Error loading Excel data: {e}")
            return []

    def perform_google_search(self, company_name):
        """Perform Google search for company HR LinkedIn profiles"""
        try:
            # Construct search query
            search_query = f'"{company_name}" HR linkedin profile'
            self.logger.info(f"Searching Google for: {search_query}")

            # Try different Google URLs
            google_urls = [
                "https://www.google.com",
                "https://google.com",
                "https://www.google.com/search"
            ]

            search_successful = False
            for google_url in google_urls:
                try:
                    self.logger.debug(f"Trying Google URL: {google_url}")
                    self.driver.get(google_url)
                    self.intelligent_wait(3, 5)

                    # Check if we need to accept cookies or handle consent
                    try:
                        # Look for common consent/cookie buttons
                        consent_buttons = [
                            'button[id*="accept"]',
                            'button[id*="consent"]',
                            'button:contains("Accept")',
                            'button:contains("I agree")',
                            '#L2AGLb',  # Google's "I agree" button
                            'button[aria-label*="Accept"]'
                        ]

                        for selector in consent_buttons:
                            try:
                                consent_btn = self.driver.find_element(By.CSS_SELECTOR, selector)
                                if consent_btn.is_displayed():
                                    consent_btn.click()
                                    self.intelligent_wait(1, 2)
                                    break
                            except:
                                continue
                    except:
                        pass

                    # Find and fill search box
                    search_box = self.find_element_with_multiple_selectors(self.google_selectors['search_box'], timeout=5)
                    if search_box:
                        search_box.clear()
                        search_box.send_keys(search_query)
                        self.intelligent_wait(1, 2)

                        # Submit search
                        search_box.send_keys(Keys.RETURN)
                        self.intelligent_wait(4, 6)

                        search_successful = True
                        break
                    else:
                        self.logger.debug(f"Could not find search box on {google_url}")

                except Exception as e:
                    self.logger.debug(f"Failed with {google_url}: {e}")
                    continue

            if not search_successful:
                # Try direct search URL as fallback
                search_url = f"https://www.google.com/search?q={search_query.replace(' ', '+')}"
                self.logger.debug(f"Trying direct search URL: {search_url}")
                self.driver.get(search_url)
                self.intelligent_wait(4, 6)

            # Log current URL for debugging
            self.logger.debug(f"Current URL after search: {self.driver.current_url}")

            # Extract search results
            search_results = self.extract_search_results()
            return search_results

        except Exception as e:
            self.logger.error(f"Error performing Google search for {company_name}: {e}")
            return []

    def extract_search_results(self):
        """Extract URLs from Google search results"""
        results = []
        try:
            # Wait a bit for results to load
            self.intelligent_wait(2, 3)

            # Try multiple approaches to find search result links
            all_links = []

            # Method 1: Find all links with href containing linkedin.com
            try:
                linkedin_links = self.driver.find_elements(By.CSS_SELECTOR, 'a[href*="linkedin.com"]')
                all_links.extend(linkedin_links)
                self.logger.debug(f"Method 1: Found {len(linkedin_links)} LinkedIn links")
            except Exception as e:
                self.logger.debug(f"Method 1 failed: {e}")

            # Method 2: Use the specific selectors
            try:
                result_elements = self.find_elements_with_multiple_selectors(self.google_selectors['search_results'])
                all_links.extend(result_elements)
                self.logger.debug(f"Method 2: Found {len(result_elements)} result elements")
            except Exception as e:
                self.logger.debug(f"Method 2 failed: {e}")

            # Method 3: Find all links and filter
            try:
                all_page_links = self.driver.find_elements(By.TAG_NAME, 'a')
                for link in all_page_links:
                    href = link.get_attribute('href')
                    if href and 'linkedin.com' in href:
                        all_links.append(link)
                self.logger.debug(f"Method 3: Found {len([l for l in all_page_links if l.get_attribute('href') and 'linkedin.com' in l.get_attribute('href')])} LinkedIn links")
            except Exception as e:
                self.logger.debug(f"Method 3 failed: {e}")

            # Extract URLs from all found elements
            seen_urls = set()
            for element in all_links[:20]:  # Limit to first 20 results
                try:
                    url = element.get_attribute('href')
                    if url and url.startswith('http') and url not in seen_urls:
                        # Clean up Google redirect URLs
                        if '/url?q=' in url:
                            # Extract actual URL from Google redirect
                            import urllib.parse
                            parsed = urllib.parse.urlparse(url)
                            query_params = urllib.parse.parse_qs(parsed.query)
                            if 'q' in query_params:
                                actual_url = query_params['q'][0]
                                if actual_url.startswith('http'):
                                    url = actual_url

                        results.append(url)
                        seen_urls.add(url)
                except Exception as e:
                    self.logger.debug(f"Error extracting URL from element: {e}")
                    continue

            self.logger.info(f"Extracted {len(results)} URLs from search results")

            # Log first few URLs for debugging
            for i, url in enumerate(results[:3]):
                self.logger.debug(f"URL {i+1}: {url}")

            return results

        except Exception as e:
            self.logger.error(f"Error extracting search results: {e}")
            return []

    def categorize_urls(self, urls):
        """Categorize URLs into valid LinkedIn company profiles and invalid URLs"""
        valid_urls = []
        invalid_urls = []

        for url in urls:
            if self.is_valid_linkedin_company_url(url):
                valid_urls.append(url)
            else:
                invalid_urls.append(url)

        return valid_urls, invalid_urls

    def search_company_profiles(self, company_names):
        """Search for LinkedIn company profiles for all companies"""
        results = []

        for i, company_name in enumerate(company_names, 1):
            try:
                self.logger.info(f"Processing company {i}/{len(company_names)}: {company_name}")

                # Perform Google search
                search_urls = self.perform_google_search(company_name)

                # Categorize URLs
                valid_urls, invalid_urls = self.categorize_urls(search_urls)

                # Determine status
                if valid_urls:
                    status = "Success"
                    self.successful_searches += 1
                elif search_urls:
                    status = "No_LinkedIn_Results"
                else:
                    status = "Failed"
                    self.failed_searches += 1

                # Store results
                result = {
                    'Company_Name': company_name,
                    'Search_Date_Time': datetime.now().strftime('%Y-%m-%d %H:%M:%S'),
                    'Valid_LinkedIn_URLs': '; '.join(valid_urls) if valid_urls else '',
                    'Invalid_URLs': '; '.join(invalid_urls) if invalid_urls else '',
                    'Search_Results_Count': len(search_urls),
                    'Status': status
                }

                results.append(result)
                self.processed_companies += 1

                self.logger.info(f"Company: {company_name} | Valid URLs: {len(valid_urls)} | Invalid URLs: {len(invalid_urls)} | Status: {status}")

                # Intelligent delay between searches
                if i < len(company_names):
                    self.intelligent_wait(5, 10)

            except Exception as e:
                self.logger.error(f"Error processing company {company_name}: {e}")

                # Add failed result
                result = {
                    'Company_Name': company_name,
                    'Search_Date_Time': datetime.now().strftime('%Y-%m-%d %H:%M:%S'),
                    'Valid_LinkedIn_URLs': '',
                    'Invalid_URLs': '',
                    'Search_Results_Count': 0,
                    'Status': 'Failed'
                }
                results.append(result)
                self.failed_searches += 1

                # Continue with next company
                continue

        return results

    def save_results_to_excel(self, results, output_filename=None):
        """Save search results to Excel file"""
        try:
            if not output_filename:
                timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
                output_filename = f"linkedin_company_profiles_{timestamp}.xlsx"

            # Create DataFrame
            df = pd.DataFrame(results)

            # Save to Excel
            df.to_excel(output_filename, index=False)

            self.logger.info(f"Results saved to {output_filename}")
            return output_filename

        except Exception as e:
            self.logger.error(f"Error saving results to Excel: {e}")
            return None

    def login_to_linkedin(self):
        """Login to LinkedIn (optional - for better search results)"""
        try:
            if not self.credentials.get('email') or not self.credentials.get('password'):
                self.logger.info("No LinkedIn credentials provided, skipping login")
                return True

            self.logger.info("Logging into LinkedIn...")
            self.driver.get("https://www.linkedin.com/login")
            self.intelligent_wait(3, 5)

            # Find and fill email
            email_field = WebDriverWait(self.driver, 10).until(
                EC.presence_of_element_located((By.ID, "username"))
            )
            email_field.clear()
            email_field.send_keys(self.credentials['email'])

            # Find and fill password
            password_field = self.driver.find_element(By.ID, "password")
            password_field.clear()
            password_field.send_keys(self.credentials['password'])

            # Click login button
            login_button = self.driver.find_element(By.XPATH, "//button[@type='submit']")
            login_button.click()

            self.intelligent_wait(5, 8)

            # Check if login was successful
            if "feed" in self.driver.current_url or "linkedin.com/in/" in self.driver.current_url:
                self.logger.info("LinkedIn login successful")
                return True
            else:
                self.logger.warning("LinkedIn login may have failed")
                return False

        except Exception as e:
            self.logger.error(f"Error logging into LinkedIn: {e}")
            return False

    def run_automation(self, excel_path, output_filename=None, login_to_linkedin=False):
        """Run the complete automation process"""
        try:
            self.logger.info("Starting LinkedIn Company Profile Search automation")

            # Setup WebDriver
            if not self.setup_driver():
                raise Exception("Failed to setup WebDriver")

            # Login to LinkedIn if requested
            if login_to_linkedin:
                self.login_to_linkedin()

            # Load company names from Excel
            company_names = self.load_excel_data(excel_path)
            if not company_names:
                raise Exception("No company names loaded from Excel file")

            # Search for company profiles
            results = self.search_company_profiles(company_names)

            # Save results
            output_file = self.save_results_to_excel(results, output_filename)

            # Print summary
            self.print_summary(results, output_file)

            return results

        except Exception as e:
            self.logger.error(f"Error in automation process: {e}")
            return []

        finally:
            if self.driver:
                self.driver.quit()
                self.logger.info("WebDriver closed")

    def print_summary(self, results, output_file):
        """Print automation summary"""
        print("\n" + "="*60)
        print("LINKEDIN COMPANY PROFILE SEARCH SUMMARY")
        print("="*60)
        print(f"Total companies processed: {self.processed_companies}")
        print(f"Successful searches: {self.successful_searches}")
        print(f"Failed searches: {self.failed_searches}")
        print(f"Success rate: {(self.successful_searches/self.processed_companies*100):.1f}%" if self.processed_companies > 0 else "0%")
        print(f"Results saved to: {output_file}")
        print("="*60)

def main():
    """Main function to run the automation"""
    # Configuration - Use relative path from metdata directory
    EXCEL_FILE_PATH = r"..\linkedin_jobs_data_20250626_222310_001.xlsx"

    # LinkedIn credentials (optional)
    credentials = {
        'email': '',  # Add your LinkedIn email
        'password': ''  # Add your LinkedIn password
    }

    # Initialize automation
    automation = LinkedInCompanyProfileSearch(
        headless=False,  # Set to True for headless mode
        credentials=credentials
    )

    # Run automation
    results = automation.run_automation(
        excel_path=EXCEL_FILE_PATH,
        output_filename=None,  # Will auto-generate filename
        login_to_linkedin=False  # Set to True if you want to login to LinkedIn
    )

    return results

if __name__ == "__main__":
    main()
